# Dual Image Format System

This project now supports two image formats optimized for different use cases on e-paper displays:

## Image Formats

### 1. Grayscale Images (4 levels)
- **File naming**: `filename.png`
- **Bits per pixel**: 2
- **Levels**: White, <PERSON> Gray, <PERSON> Gray, **Black**
- **Features**: Dithering enabled for better detail
- **Best for**: Photos, detailed graphics, backgrounds, charts

### 2. Black & White Images (2 levels)
- **File naming**: `filename_bw.png`
- **Bits per pixel**: 1
- **Levels**: Black, White
- **Features**: Pure binary, memory efficient
- **Best for**: Icons, logos, simple graphics, text

## Usage

### C++ Image Classes

The system provides a simple, unified C++ class that automatically detects the image format:

```cpp
#include "images/image_bitmaps.h"

// BaseImage automatically detects format and uses the correct drawing method
BaseImage background(IMAGE_BACKGROUND);     // Auto-detects grayscale format
background.draw(EDisplay, 0, 0);             // Uses drawGreyPixmap automatically

BaseImage icon(IMAGE_BATTERY_ICON_BW);       // Auto-detects black & white format
icon.draw(EDisplay, 100, 50);                // Uses drawBitmap automatically

// Access image properties
int width = icon.width();
int height = icon.height();
int xOffset = icon.x_offset();
int yOffset = icon.y_offset();
int bitsPerPixel = icon.bits_per_pixel();    // 1 for B&W, 2 for grayscale
```

### Image Processing

1. **Place images** in the `data-export/` folder
2. **Run preprocessing**: `python preprocess_files.py`
3. **Generated headers** appear in `include/images/`

### File Structure

```
data-export/
├── background.png          # → 4-level grayscale
├── chart.png              # → 4-level grayscale  
├── battery_icon_bw.png     # → black & white
└── logo_bw.png            # → black & white

include/images/
├── background.h
├── chart.h
├── battery_icon_bw.h
├── logo_bw.h
└── image_bitmaps.h        # Master header with classes
```

## Memory Usage

- **Grayscale**: 2 bits per pixel (4 pixels per byte)
- **Black & White**: 1 bit per pixel (8 pixels per byte)

Example for 100x100 pixel image:
- Grayscale: 2,500 bytes
- Black & White: 1,250 bytes (50% savings)

## Processing Features

### Grayscale Images
- Floyd-Steinberg dithering for better detail
- Automatic quantization to 4 levels (0, 85, 170, 255)
- Cropping to remove transparent/white borders

### Black & White Images
- Threshold conversion (< 127 → white, ≥ 127 → black)
- No dithering (pure binary)
- Cropping to remove transparent/white borders

## Legacy Support

The old method still works for backward compatibility:

```cpp
const ImageBitmap* image = getImage(IMAGE_BACKGROUND);

if (image->bits_per_pixel == 2) {
    // Grayscale
    EDisplay.drawGreyPixmap(image->bitmap, 2, 
                           image->x_offset, image->y_offset,
                           image->width, image->height);
} else if (image->bits_per_pixel == 1) {
    // Black & white
    EDisplay.drawBitmap(image->x_offset, image->y_offset,
                       image->bitmap, image->width, image->height, 
                       GxEPD_BLACK);
}
```

## Best Practices

1. **Use grayscale** for detailed images that benefit from dithering
2. **Use black & white** for simple graphics to save memory
3. **Name files appropriately** (`_bw` suffix for black & white)
4. **Test both formats** to see which works better for your content
5. **Consider memory constraints** when choosing formats

## Examples

See `example_image_usage.cpp` for comprehensive usage examples.
