# PlatformIO extra script to import image processing functionality
# Import the image processing module and trigger PlatformIO integration

try:
    # Import PlatformIO environment
    Import("env")

    # Import our image processing functions
    from image_processing.preprocess_files import preprocess_files
    from image_processing.generate_test_images import generate_test_images

    # Ensure image headers exist before any compilation
    def ensure_headers_exist(source, target, env):
        """Ensure image headers exist before compilation starts."""
        import os
        from pathlib import Path

        # Check if the master header exists
        master_header = Path("include/images/image_bitmaps.h")
        if not master_header.exists():
            print("Image headers not found, generating...")
            preprocess_files()

    # Run preprocessing immediately when the script loads
    ensure_headers_exist(None, None, env)

    # Add custom target for manual preprocessing (existing images only)
    env.AddCustomTarget(
        name="preprocess_files",
        dependencies=None,
        actions=[preprocess_files],
        title="Preprocess Images",
        description="Preprocess existing PNG files to C++ headers (no test image generation)",
    )

    # Add custom target for test image generation
    def generate_test_images_only(source, target, env):
        """Generate test images without processing to headers."""
        generate_test_images(process_to_headers=False)

    env.AddCustomTarget(
        name="generate_test_images",
        dependencies=None,
        actions=[generate_test_images_only],
        title="Generate Test Images",
        description="Generate test images (BW and grayscale) for testing",
    )

    # Add custom target for test image generation with header processing
    def generate_and_process_test_images(source, target, env):
        """Generate test images and process them to headers."""
        generate_test_images(process_to_headers=True)

    env.AddCustomTarget(
        name="generate_test_images_with_headers",
        dependencies=None,
        actions=[generate_and_process_test_images],
        title="Generate Test Images + Headers",
        description="Generate test images and process them to C++ headers",
    )

    # Add cleanup function
    def cleanup_generated_files(source, target, env):
        """Clean up all generated files including headers, previews, and test images."""
        import os
        from pathlib import Path

        print("Cleaning up generated files...")

        # Clean up C++ headers
        headers_dir = Path("include/images")
        if headers_dir.exists():
            for header_file in headers_dir.glob("*.h"):
                header_file.unlink()
                print(f"Removed: {header_file}")

        # Clean up preview images from data-export
        data_export_dir = Path("data-export")
        if data_export_dir.exists():
            for preview_file in data_export_dir.glob("*_preview.png"):
                preview_file.unlink()
                print(f"Removed: {preview_file}")

        # Clean up preview images from include/images
        if headers_dir.exists():
            for preview_file in headers_dir.glob("*_preview.png"):
                preview_file.unlink()
                print(f"Removed: {preview_file}")

        print("Cleanup completed!")

    # Add cleanup function for test images only
    def cleanup_test_images(source, target, env):
        """Clean up only test images, leaving other images intact."""
        import os
        from pathlib import Path

        print("Cleaning up test images...")

        data_export_dir = Path("data-export")
        headers_dir = Path("include/images")

        # Clean up test images
        test_images = [
            data_export_dir / "test_bw.png",
            data_export_dir / "test_grayscale.png"
        ]
        for test_image in test_images:
            if test_image.exists():
                test_image.unlink()
                print(f"Removed: {test_image}")

        # Clean up test image headers
        test_headers = [
            headers_dir / "test_bw.h",
            headers_dir / "test_grayscale.h"
        ]
        for test_header in test_headers:
            if test_header.exists():
                test_header.unlink()
                print(f"Removed: {test_header}")

        # Clean up test image previews
        test_previews = [
            data_export_dir / "test_bw_preview.png",
            data_export_dir / "test_grayscale_preview.png"
        ]
        for test_preview in test_previews:
            if test_preview.exists():
                test_preview.unlink()
                print(f"Removed: {test_preview}")

        print("Test image cleanup completed!")

    # Add custom cleanup targets
    env.AddCustomTarget(
        name="cleanup_images",
        dependencies=None,
        actions=[cleanup_generated_files],
        title="Cleanup All Generated Files",
        description="Remove all generated C++ headers and preview images",
    )

    env.AddCustomTarget(
        name="cleanup_test_images",
        dependencies=None,
        actions=[cleanup_test_images],
        title="Cleanup Test Images",
        description="Remove only test images and their generated files",
    )

except NameError:
    # If Import is not available, we're not in PlatformIO context
    print("Warning: Not running in PlatformIO context, skipping image preprocessing integration")
except ImportError as e:
    print(f"Error importing image processing module: {e}")